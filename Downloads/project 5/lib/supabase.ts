import { createClient } from '@supabase/supabase-js';
import * as SecureStore from 'expo-secure-store';

// For demo purposes, using a public Supabase instance
// In production, you should use your own Supabase project
const supabaseUrl = 'https://your-project.supabase.co';
const supabaseAnonKey = 'your-anon-key';

// Custom storage adapter for Expo SecureStore
const ExpoSecureStoreAdapter = {
  getItem: (key: string) => {
    return SecureStore.getItemAsync(key);
  },
  setItem: (key: string, value: string) => {
    SecureStore.setItemAsync(key, value);
  },
  removeItem: (key: string) => {
    SecureStore.deleteItemAsync(key);
  },
};

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: ExpoSecureStoreAdapter,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types
export interface User {
  id: string;
  email?: string;
  username: string;
  major?: string;
  year?: string;
  avatar_url?: string;
  is_verified: boolean;
  is_anonymous: boolean;
  created_at: string;
  updated_at: string;
}

export interface Listing {
  id: string;
  user_id: string;
  title: string;
  description: string;
  price: number;
  course_code?: string;
  category: 'Books' | 'Electronics' | 'Supplies' | 'Furniture';
  condition: 'New' | 'Like New' | 'Good' | 'Fair' | 'Poor';
  image_url?: string;
  is_available: boolean;
  created_at: string;
  updated_at: string;
  user?: User;
}

export interface ChatRoom {
  id: string;
  name: string;
  type: 'major' | 'course' | 'year' | 'general';
  identifier: string; // e.g., 'CS', 'CS101', 'Freshman'
  description?: string;
  member_count: number;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: string;
  chat_room_id: string;
  user_id: string;
  content: string;
  is_reported: boolean;
  created_at: string;
  user?: User;
}

export interface Event {
  id: string;
  user_id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  category: 'Study' | 'Social' | 'Career' | 'Sports' | 'Academic';
  max_attendees?: number;
  current_attendees: number;
  created_at: string;
  updated_at: string;
  user?: User;
}

export interface RoommateProfile {
  id: string;
  user_id: string;
  bio: string;
  preferences: {
    sleep_schedule: string;
    smoking: string;
    pets: string;
    cleanliness: string;
    social_level: string;
  };
  compatibility_score?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  user?: User;
}
