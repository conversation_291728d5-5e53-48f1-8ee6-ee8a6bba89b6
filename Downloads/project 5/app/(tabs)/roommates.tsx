import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Setting<PERSON>, Heart, X } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';
import { RoommateCard } from '@/components/RoommateCard';

const screenWidth = Dimensions.get('window').width;

const mockProfiles = [
  {
    id: '1',
    name: '<PERSON>',
    major: 'Computer Science',
    year: 'Junior',
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=800',
    preferences: {
      sleepSchedule: 'Early Bird',
      smoking: 'Non-smoker',
      pets: 'Cat friendly',
      cleanliness: 'Very Clean',
      socialLevel: 'Social',
    },
    bio: 'Looking for a study buddy and roommate! Love coding, hiking, and coffee.',
    compatibility: 92,
  },
  {
    id: '2',
    name: '<PERSON>',
    major: 'Biology',
    year: 'Sophomore',
    avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=800',
    preferences: {
      sleepSchedule: 'Night Owl',
      smoking: 'Non-smoker',
      pets: 'No pets',
      cleanliness: 'Clean',
      socialLevel: 'Quiet',
    },
    bio: 'Pre-med student who loves reading and peaceful environments. Looking for a quiet roommate.',
    compatibility: 78,
  },
  {
    id: '3',
    name: 'Emma Rodriguez',
    major: 'Engineering',
    year: 'Senior',
    avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=800',
    preferences: {
      sleepSchedule: 'Flexible',
      smoking: 'Non-smoker',
      pets: 'Dog friendly',
      cleanliness: 'Very Clean',
      socialLevel: 'Social',
    },
    bio: 'Engineering student who loves building projects and hosting study sessions.',
    compatibility: 85,
  },
];

export default function RoommatesScreen() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [profiles] = useState(mockProfiles);

  const handleSwipe = (direction: 'left' | 'right') => {
    if (direction === 'right') {
      // Handle match logic here
      console.log('Liked profile:', profiles[currentIndex].name);
    } else {
      // Handle pass logic here
      console.log('Passed on profile:', profiles[currentIndex].name);
    }

    // Move to next profile
    if (currentIndex < profiles.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      // Reset or show no more profiles message
      setCurrentIndex(0);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Find Roommates</Text>
        <TouchableOpacity style={styles.settingsButton}>
          <Settings color={Colors.textSecondary} size={24} />
        </TouchableOpacity>
      </View>

      {/* Card Stack */}
      <View style={styles.cardContainer}>
        {profiles.length > currentIndex && (
          <RoommateCard 
            profile={profiles[currentIndex]} 
            onSwipe={handleSwipe}
          />
        )}
      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, styles.passButton]}
          onPress={() => handleSwipe('left')}
        >
          <X color={Colors.surface} size={32} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, styles.likeButton]}
          onPress={() => handleSwipe('right')}
        >
          <Heart color={Colors.surface} size={32} />
        </TouchableOpacity>
      </View>

      {/* Progress Indicator */}
      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>
          {currentIndex + 1} of {profiles.length}
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
  },
  settingsButton: {
    padding: 8,
  },
  cardContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    gap: 40,
  },
  actionButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.text,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 6,
  },
  passButton: {
    backgroundColor: Colors.textSecondary,
  },
  likeButton: {
    backgroundColor: Colors.error,
  },
  progressContainer: {
    alignItems: 'center',
    paddingBottom: 20,
  },
  progressText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
});