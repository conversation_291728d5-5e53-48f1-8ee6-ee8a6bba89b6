import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Plus, Calendar, MapPin, Clock } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';
import { EventCard } from '@/components/EventCard';

const mockEvents = [
  {
    id: '1',
    title: 'CS Study Group',
    description: 'Weekly study session for Computer Science students. We\'ll be covering algorithms and data structures.',
    date: '2024-01-15',
    time: '6:00 PM',
    location: 'Library Room 204',
    attendees: 12,
    maxAttendees: 20,
    category: 'Study',
    organizer: '<PERSON>',
    isAttending: true,
  },
  {
    id: '2',
    title: 'Campus Job Fair',
    description: 'Connect with top employers and explore internship opportunities.',
    date: '2024-01-18',
    time: '10:00 AM',
    location: 'Student Center',
    attendees: 89,
    maxAttendees: 150,
    category: 'Career',
    organizer: 'Career Services',
    isAttending: false,
  },
  {
    id: '3',
    title: 'Basketball Game',
    description: 'Cheer on our university team against State University!',
    date: '2024-01-20',
    time: '7:30 PM',
    location: 'Sports Arena',
    attendees: 234,
    maxAttendees: 500,
    category: 'Sports',
    organizer: 'Athletic Department',
    isAttending: false,
  },
  {
    id: '4',
    title: 'Programming Workshop',
    description: 'Learn React Native development with hands-on coding exercises.',
    date: '2024-01-22',
    time: '2:00 PM',
    location: 'Computer Lab A',
    attendees: 25,
    maxAttendees: 30,
    category: 'Workshop',
    organizer: 'Tech Club',
    isAttending: true,
  },
];

const categories = ['All', 'Study', 'Career', 'Sports', 'Workshop', 'Social'];

export default function EventsScreen() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [filteredEvents, setFilteredEvents] = useState(mockEvents);

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    if (category === 'All') {
      setFilteredEvents(mockEvents);
    } else {
      setFilteredEvents(mockEvents.filter(event => event.category === category));
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Text style={styles.title}>Events</Text>
          <TouchableOpacity style={styles.createButton}>
            <Plus color={Colors.surface} size={20} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Category Filter */}
      <View style={styles.categoriesContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesScrollContent}
        >
          {categories.map((category) => (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryButton,
                selectedCategory === category && styles.selectedCategoryButton,
              ]}
              onPress={() => handleCategoryChange(category)}
            >
              <Text
                style={[
                  styles.categoryText,
                  selectedCategory === category && styles.selectedCategoryText,
                ]}
              >
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Events List */}
      <ScrollView style={styles.eventsList} showsVerticalScrollIndicator={false}>
        {filteredEvents.map((event) => (
          <EventCard key={event.id} event={event} />
        ))}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.surface,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
  },
  createButton: {
    backgroundColor: Colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoriesContainer: {
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  categoriesScrollContent: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  categoryButton: {
    backgroundColor: Colors.background,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  selectedCategoryButton: {
    backgroundColor: Colors.primary,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textSecondary,
  },
  selectedCategoryText: {
    color: Colors.surface,
  },
  eventsList: {
    flex: 1,
  },
  bottomSpacing: {
    height: 20,
  },
});