import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Search, Hash, Users } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';
import { ChatRoomCard } from '@/components/ChatRoomCard';

const mockChatRooms = [
  {
    id: '1',
    name: 'Computer Science',
    type: 'major',
    members: 245,
    lastMessage: 'Anyone taking OS this semester?',
    lastActivity: '2 min ago',
    unreadCount: 3,
  },
  {
    id: '2',
    name: 'CS 101 - Intro Programming',
    type: 'course',
    members: 89,
    lastMessage: 'Assignment 3 due tomorrow!',
    lastActivity: '15 min ago',
    unreadCount: 1,
  },
  {
    id: '3',
    name: 'Biology Major',
    type: 'major',
    members: 156,
    lastMessage: 'Study group for biochem?',
    lastActivity: '1 hour ago',
    unreadCount: 0,
  },
  {
    id: '4',
    name: 'Freshmen 2024',
    type: 'year',
    members: 312,
    lastMessage: 'Welcome week events!',
    lastActivity: '3 hours ago',
    unreadCount: 7,
  },
  {
    id: '5',
    name: 'MATH 201 - Calculus II',
    type: 'course',
    members: 67,
    lastMessage: 'Integration by parts help?',
    lastActivity: '5 hours ago',
    unreadCount: 0,
  },
];

export default function ChatScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  const filteredRooms = mockChatRooms.filter(room => {
    const matchesSearch = room.name.toLowerCase().includes(searchQuery.toLowerCase());
    if (activeTab === 'all') return matchesSearch;
    return matchesSearch && room.type === activeTab;
  });

  const tabs = [
    { key: 'all', label: 'All', icon: Hash },
    { key: 'major', label: 'Majors', icon: Users },
    { key: 'course', label: 'Courses', icon: Hash },
    { key: 'year', label: 'Year', icon: Users },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Chat Rooms</Text>
        
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Search color={Colors.textSecondary} size={20} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search chat rooms..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor={Colors.textSecondary}
          />
        </View>
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.tabsScrollContent}
        >
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <TouchableOpacity
                key={tab.key}
                style={[
                  styles.tab,
                  activeTab === tab.key && styles.activeTab,
                ]}
                onPress={() => setActiveTab(tab.key)}
              >
                <Icon
                  color={activeTab === tab.key ? Colors.surface : Colors.textSecondary}
                  size={16}
                />
                <Text
                  style={[
                    styles.tabText,
                    activeTab === tab.key && styles.activeTabText,
                  ]}
                >
                  {tab.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

      {/* Chat Rooms List */}
      <ScrollView style={styles.roomsList} showsVerticalScrollIndicator={false}>
        {filteredRooms.map((room) => (
          <ChatRoomCard key={room.id} room={room} />
        ))}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.surface,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 48,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
  },
  tabsContainer: {
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  tabsScrollContent: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  activeTab: {
    backgroundColor: Colors.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textSecondary,
    marginLeft: 6,
  },
  activeTabText: {
    color: Colors.surface,
  },
  roomsList: {
    flex: 1,
  },
  bottomSpacing: {
    height: 20,
  },
});