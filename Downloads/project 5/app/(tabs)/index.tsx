import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Search, Plus, Filter } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';
import { MarketplaceCard } from '@/components/MarketplaceCard';
import { CategoryFilter } from '@/components/CategoryFilter';

const mockListings = [
  {
    id: '1',
    title: 'Calculus Textbook',
    price: '$45',
    course: 'MATH 101',
    image: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=800',
    seller: '<PERSON>',
    condition: 'Like New',
    category: 'Books',
  },
  {
    id: '2',
    title: 'MacBook Air M1',
    price: '$800',
    course: 'CS 201',
    image: 'https://images.pexels.com/photos/205421/pexels-photo-205421.jpeg?auto=compress&cs=tinysrgb&w=800',
    seller: '<PERSON>',
    condition: 'Good',
    category: 'Electronics',
  },
  {
    id: '3',
    title: 'Scientific Calculator',
    price: '$25',
    course: 'PHYS 101',
    image: 'https://images.pexels.com/photos/6238297/pexels-photo-6238297.jpeg?auto=compress&cs=tinysrgb&w=800',
    seller: 'Emma L.',
    condition: 'Excellent',
    category: 'Supplies',
  },
  {
    id: '4',
    title: 'Desk Lamp',
    price: '$15',
    course: null,
    image: 'https://images.pexels.com/photos/1112598/pexels-photo-1112598.jpeg?auto=compress&cs=tinysrgb&w=800',
    seller: 'Alex R.',
    condition: 'Good',
    category: 'Furniture',
  },
];

const categories = ['All', 'Books', 'Electronics', 'Supplies', 'Furniture'];

export default function MarketplaceScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [filteredListings, setFilteredListings] = useState(mockListings);

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    if (category === 'All') {
      setFilteredListings(mockListings);
    } else {
      setFilteredListings(mockListings.filter(item => item.category === category));
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    const filtered = mockListings.filter(item =>
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      (item.course && item.course.toLowerCase().includes(query.toLowerCase()))
    );
    setFilteredListings(selectedCategory === 'All' ? filtered : 
      filtered.filter(item => item.category === selectedCategory));
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Text style={styles.title}>Marketplace</Text>
          <TouchableOpacity style={styles.postButton}>
            <Plus color={Colors.surface} size={20} />
          </TouchableOpacity>
        </View>
        
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Search color={Colors.textSecondary} size={20} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search items or course codes..."
            value={searchQuery}
            onChangeText={handleSearch}
            placeholderTextColor={Colors.textSecondary}
          />
          <TouchableOpacity style={styles.filterButton}>
            <Filter color={Colors.textSecondary} size={20} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Category Filter */}
      <CategoryFilter
        categories={categories}
        selectedCategory={selectedCategory}
        onCategoryChange={handleCategoryChange}
      />

      {/* Listings */}
      <ScrollView style={styles.listingsContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.listingsGrid}>
          {filteredListings.map((listing) => (
            <MarketplaceCard key={listing.id} listing={listing} />
          ))}
        </View>
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.surface,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
  },
  postButton: {
    backgroundColor: Colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 48,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
  },
  filterButton: {
    marginLeft: 12,
  },
  listingsContainer: {
    flex: 1,
  },
  listingsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 20,
    justifyContent: 'space-between',
  },
  bottomSpacing: {
    height: 20,
  },
});