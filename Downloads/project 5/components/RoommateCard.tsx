import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Colors } from '@/constants/Colors';

const screenWidth = Dimensions.get('window').width;
const cardWidth = screenWidth - 40;

interface RoommateProfile {
  id: string;
  name: string;
  major: string;
  year: string;
  avatar: string;
  preferences: {
    sleepSchedule: string;
    smoking: string;
    pets: string;
    cleanliness: string;
    socialLevel: string;
  };
  bio: string;
  compatibility: number;
}

interface RoommateCardProps {
  profile: RoommateProfile;
  onSwipe: (direction: 'left' | 'right') => void;
}

export function RoommateCard({ profile }: RoommateCardProps) {
  const getCompatibilityColor = (score: number) => {
    if (score >= 90) return Colors.success;
    if (score >= 75) return Colors.accent;
    if (score >= 60) return Colors.warning;
    return Colors.error;
  };

  return (
    <View style={styles.card}>
      {/* Profile Image */}
      <Image source={{ uri: profile.avatar }} style={styles.avatar} />
      
      {/* Compatibility Score */}
      <View style={[styles.compatibilityBadge, { backgroundColor: getCompatibilityColor(profile.compatibility) }]}>
        <Text style={styles.compatibilityText}>{profile.compatibility}% Match</Text>
      </View>

      {/* Profile Info */}
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name}>{profile.name}</Text>
          <Text style={styles.details}>{profile.major} • {profile.year}</Text>
        </View>

        <Text style={styles.bio}>{profile.bio}</Text>

        {/* Preferences */}
        <ScrollView style={styles.preferencesContainer} showsVerticalScrollIndicator={false}>
          <Text style={styles.preferencesTitle}>Preferences</Text>
          {Object.entries(profile.preferences).map(([key, value]) => (
            <View key={key} style={styles.preferenceRow}>
              <Text style={styles.preferenceLabel}>
                {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:
              </Text>
              <Text style={styles.preferenceValue}>{value}</Text>
            </View>
          ))}
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    width: cardWidth,
    backgroundColor: Colors.surface,
    borderRadius: 20,
    shadowColor: Colors.text,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
    overflow: 'hidden',
  },
  avatar: {
    width: '100%',
    height: 240,
  },
  compatibilityBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  compatibilityText: {
    color: Colors.surface,
    fontSize: 12,
    fontWeight: 'bold',
  },
  content: {
    padding: 20,
  },
  header: {
    marginBottom: 12,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  details: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  bio: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 20,
    marginBottom: 20,
  },
  preferencesContainer: {
    maxHeight: 200,
  },
  preferencesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  preferenceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  preferenceLabel: {
    fontSize: 14,
    color: Colors.text,
    flex: 1,
  },
  preferenceValue: {
    fontSize: 14,
    color: Colors.secondary,
    fontWeight: '500',
  },
});