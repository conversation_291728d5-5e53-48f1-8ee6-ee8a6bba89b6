import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Users } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';

interface ChatRoom {
  id: string;
  name: string;
  type: string;
  members: number;
  lastMessage: string;
  lastActivity: string;
  unreadCount: number;
}

interface ChatRoomCardProps {
  room: ChatRoom;
}

export function ChatRoomCard({ room }: ChatRoomCardProps) {
  return (
    <TouchableOpacity style={styles.card}>
      <View style={styles.content}>
        <View style={styles.header}>
          <View style={styles.nameRow}>
            <Text style={styles.name}>{room.name}</Text>
            {room.unreadCount > 0 && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadText}>
                  {room.unreadCount > 9 ? '9+' : room.unreadCount}
                </Text>
              </View>
            )}
          </View>
          <Text style={styles.activity}>{room.lastActivity}</Text>
        </View>
        
        <Text style={styles.lastMessage} numberOfLines={1}>
          {room.lastMessage}
        </Text>
        
        <View style={styles.footer}>
          <View style={styles.membersRow}>
            <Users color={Colors.textSecondary} size={14} />
            <Text style={styles.members}>{room.members} members</Text>
          </View>
          <View style={[styles.typeBadge, { backgroundColor: getTypeColor(room.type) }]}>
            <Text style={styles.typeText}>{room.type}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
}

function getTypeColor(type: string): string {
  switch (type) {
    case 'major':
      return Colors.secondary;
    case 'course':
      return Colors.accent;
    case 'year':
      return Colors.success;
    default:
      return Colors.textSecondary;
  }
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.surface,
    marginHorizontal: 20,
    marginVertical: 6,
    borderRadius: 12,
    shadowColor: Colors.text,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    flex: 1,
  },
  unreadBadge: {
    backgroundColor: Colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  unreadText: {
    color: Colors.surface,
    fontSize: 12,
    fontWeight: 'bold',
  },
  activity: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 8,
  },
  lastMessage: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 12,
    lineHeight: 18,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  membersRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  members: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginLeft: 4,
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
  },
  typeText: {
    fontSize: 11,
    color: Colors.surface,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
});