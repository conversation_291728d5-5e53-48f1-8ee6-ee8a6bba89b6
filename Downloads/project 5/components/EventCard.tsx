import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Calendar, MapPin, Clock, Users, UserCheck } from 'lucide-react-native';
import { Colors } from '@/constants/Colors';

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  attendees: number;
  maxAttendees: number;
  category: string;
  organizer: string;
  isAttending: boolean;
}

interface EventCardProps {
  event: Event;
}

export function EventCard({ event }: EventCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      weekday: 'short'
    });
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Study': return Colors.secondary;
      case 'Career': return Colors.success;
      case 'Sports': return Colors.error;
      case 'Workshop': return Colors.accent;
      case 'Social': return '#8B5CF6';
      default: return Colors.textSecondary;
    }
  };

  return (
    <TouchableOpacity style={styles.card}>
      <View style={styles.header}>
        <View style={styles.dateContainer}>
          <Calendar color={Colors.primary} size={16} />
          <Text style={styles.date}>{formatDate(event.date)}</Text>
        </View>
        <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(event.category) }]}>
          <Text style={styles.categoryText}>{event.category}</Text>
        </View>
      </View>

      <Text style={styles.title}>{event.title}</Text>
      <Text style={styles.description} numberOfLines={2}>
        {event.description}
      </Text>

      <View style={styles.details}>
        <View style={styles.detailRow}>
          <Clock color={Colors.textSecondary} size={16} />
          <Text style={styles.detailText}>{event.time}</Text>
        </View>
        <View style={styles.detailRow}>
          <MapPin color={Colors.textSecondary} size={16} />
          <Text style={styles.detailText}>{event.location}</Text>
        </View>
        <View style={styles.detailRow}>
          <Users color={Colors.textSecondary} size={16} />
          <Text style={styles.detailText}>
            {event.attendees}/{event.maxAttendees} attending
          </Text>
        </View>
      </View>

      <View style={styles.footer}>
        <Text style={styles.organizer}>by {event.organizer}</Text>
        <TouchableOpacity 
          style={[
            styles.rsvpButton,
            event.isAttending && styles.attendingButton
          ]}
        >
          {event.isAttending ? (
            <>
              <UserCheck color={Colors.surface} size={16} />
              <Text style={styles.attendingButtonText}>Attending</Text>
            </>
          ) : (
            <Text style={styles.rsvpButtonText}>RSVP</Text>
          )}
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.surface,
    marginHorizontal: 20,
    marginVertical: 8,
    borderRadius: 16,
    padding: 16,
    shadowColor: Colors.text,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  date: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
    marginLeft: 6,
  },
  categoryBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    color: Colors.surface,
    fontSize: 12,
    fontWeight: '600',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 20,
    marginBottom: 16,
  },
  details: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginLeft: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  organizer: {
    fontSize: 12,
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
  rsvpButton: {
    backgroundColor: Colors.accent,
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  rsvpButtonText: {
    color: Colors.text,
    fontSize: 14,
    fontWeight: '600',
  },
  attendingButton: {
    backgroundColor: Colors.success,
    flexDirection: 'row',
    alignItems: 'center',
  },
  attendingButtonText: {
    color: Colors.surface,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
});