import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { Colors } from '@/constants/Colors';

const screenWidth = Dimensions.get('window').width;
const cardWidth = (screenWidth - 60) / 2;

interface Listing {
  id: string;
  title: string;
  price: string;
  course: string | null;
  image: string;
  seller: string;
  condition: string;
  category: string;
}

interface MarketplaceCardProps {
  listing: Listing;
}

export function MarketplaceCard({ listing }: MarketplaceCardProps) {
  return (
    <TouchableOpacity style={styles.card}>
      <Image source={{ uri: listing.image }} style={styles.image} />
      <View style={styles.content}>
        <Text style={styles.title} numberOfLines={2}>
          {listing.title}
        </Text>
        {listing.course && (
          <Text style={styles.course}>{listing.course}</Text>
        )}
        <View style={styles.priceRow}>
          <Text style={styles.price}>{listing.price}</Text>
          <Text style={styles.condition}>{listing.condition}</Text>
        </View>
        <Text style={styles.seller}>by {listing.seller}</Text>
        <TouchableOpacity style={styles.contactButton}>
          <Text style={styles.contactButtonText}>Contact Seller</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    width: cardWidth,
    backgroundColor: Colors.surface,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: Colors.text,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  image: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  content: {
    padding: 12,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
    lineHeight: 18,
  },
  course: {
    fontSize: 12,
    color: Colors.secondary,
    fontWeight: '500',
    marginBottom: 6,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  condition: {
    fontSize: 11,
    color: Colors.success,
    backgroundColor: Colors.background,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  seller: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  contactButton: {
    backgroundColor: Colors.accent,
    borderRadius: 8,
    paddingVertical: 8,
    alignItems: 'center',
  },
  contactButtonText: {
    color: Colors.text,
    fontSize: 12,
    fontWeight: '600',
  },
});